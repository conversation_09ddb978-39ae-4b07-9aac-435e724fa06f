const fs = require('fs');
const ts = require('typescript');

// Read the TypeScript file
const fileName = 'src/skywind/services/contextFinalizationTimeCalculator.ts';
const sourceCode = fs.readFileSync(fileName, 'utf8');

// Create a simple TypeScript program to check syntax
const compilerOptions = {
  target: ts.ScriptTarget.ES2019,
  module: ts.ModuleKind.CommonJS,
  noEmitOnError: true,
  skipLibCheck: true,
  noImplicitAny: false
};

// Create a source file
const sourceFile = ts.createSourceFile(
  fileName,
  sourceCode,
  ts.ScriptTarget.ES2019,
  true
);

// Check for syntax errors
const program = ts.createProgram([fileName], compilerOptions, {
  getSourceFile: (name) => name === fileName ? sourceFile : undefined,
  writeFile: () => {},
  getCurrentDirectory: () => process.cwd(),
  getDirectories: () => [],
  fileExists: () => true,
  readFile: () => sourceCode,
  getCanonicalFileName: (name) => name,
  useCaseSensitiveFileNames: () => true,
  getNewLine: () => '\n'
});

const diagnostics = ts.getPreEmitDiagnostics(program);

if (diagnostics.length === 0) {
  console.log('✅ TypeScript syntax is valid!');
} else {
  console.log('❌ TypeScript syntax errors found:');
  diagnostics.forEach(diagnostic => {
    if (diagnostic.file) {
      const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
      const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
      console.log(`${diagnostic.file.fileName} (${line + 1},${character + 1}): ${message}`);
    } else {
      console.log(ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n'));
    }
  });
}
