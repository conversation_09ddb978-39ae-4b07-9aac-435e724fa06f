{"name": "@skywind-group/sw-game-engine", "version": "7.7.0", "description": "", "author": "<PERSON> <<EMAIL>>", "license": "ISC", "main": "lib/index.js", "typings": "resources/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.json", "gulp": "gulp", "test": "npm run only-test", "tslint": "tslint -c tslint.json -p tsconfig.json src/*.ts src/**/*.ts src/**/**/*.ts src/**/**/**/*.ts", "version": "mkdir -p lib/skywind && echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version", "only-test": "RETRIES_SLEEP_TIMEOUT=0 istanbul cover --print both node_modules/mocha/bin/_mocha -- -R spec lib/test/**/**/*.spec.js lib/test/**/*.spec.js lib/test/*.spec.js", "sonar": "gulp sonar", "remap": "remap-istanbul -i ./coverage/coverage.raw.json -o ./coverage/lcov-final.lcov -t lcovonly -b src", "install-games": "gulp install-games", "dev": "gulp install-games && npm run compile && npm run version && node -r dotenv/config lib/test/test.js", "test:ts": "RETRIES_SLEEP_TIMEOUT=0 node_modules/mocha/bin/_mocha --require ts-node/register spec src/test/**/**/*.spec.ts src/test/**/*.spec.ts src/test/*.spec.ts", "test:mocha": "mocha --require ts-node/register --require dotenv/config"}, "devDependencies": {"@skywind-group/sw-fufish": "3.5.0", "@skywind-group/sw-game-autoplayer": "0.2.3", "@skywind-group/sw-random-cheating": "1.0.3-rng.3", "@skywind-group/sw-random-cs": "1.0.8", "@skywind-group/sw-random-cs-cheating": "1.0.8", "@skywind-group/sw-sm-result-builder": "0.1.11", "@skywind-group/sw_al": "0.3.0", "@skywind-group/sw_cada": "0.1.6", "@skywind-group/sw_elpr": "0.2.17", "@skywind-group/sw_ex_fb_coins_override": "0.1.4", "@skywind-group/sw_ex_fb_override": "0.1.2", "@skywind-group/sw_ex_req_validation": "0.0.2", "@skywind-group/sw_mrmnky": "1.2.1", "@skywind-group/sw_remamere": "0.6.1", "@skywind-group/sw_rm_ab_jp": "2.0.2", "@skywind-group/sw_ug_coins_override": "0.1.3", "@types/benchmark": "1.0.30", "@types/chai": "4.2.4", "@types/chai-as-promised": "0.0.31", "@types/commander": "2.12.2", "@types/cookiejar": "2.1.1", "@types/deep-equal-in-any-order": "1.0.1", "@types/jsonwebtoken": "9.0.9", "@types/lodash": "4.14.110", "@types/mocha": "2.2.41", "@types/node": "14.18.63", "@types/request-ip": "0.0.41", "@types/sinon": "2.2.1", "@types/sinon-chai": "2.7.28", "@types/superagent": "^8.1.7", "@types/uuid": "9.0.8", "@types/validator": "10.11.3", "@types/socket.io-v2": "npm:@types/socket.io@2.1.13", "agentkeepalive": "^4.5.0", "superagent": "^9.0.2", "jsonwebtoken": "9.0.2", "generic-pool": "3.9.0", "ioredis": "5.5.0", "uuid": "9.0.1", "node-schedule": "2.1.1", "fast-xml-parser": "4.4.1", "async-file": "2.0.2", "benchmark": "2.1.4", "bole": "3.0.2", "bole-console": "0.1.10", "chai": "4.2.0", "chai-as-promised": "7.0.0", "chai-exclude": "2.0.2", "deep-equal-in-any-order": "1.0.21", "dotenv": "16.0.3", "event-loop-stats": "1.0.0", "gelf-stream": "1.1.1", "gulp": "4.0.2", "istanbul": "1.1.0-alpha.1", "socket.io-client": "4.8.1", "lodash": "4.17.21", "measured": "1.1.0", "mocha": "3.5.2", "mocha-typescript": "1.1.12", "msgpack": "^1.0.2", "remap-istanbul": "0.12.0", "sha1": "1.1.1", "sinon": "2.3.5", "sonarqube-scanner": "2.1.1", "superagent-mocker": "^0.5.2", "supertest": "3.4.2", "ts-node": "6.0.5", "tslint": "5.0.0", "typescript": "4.9.5"}, "peerDependencies": {"@skywind-group/sw_ex_fb_override": "0.1.2", "@skywind-group/sw_ug_coins_override": "0.1.3", "agentkeepalive": "4.5.0", "superagent": "9.0.2", "jsonwebtoken": "9.0.2", "generic-pool": "3.9.0", "ioredis": "5.5.0", "uuid": "9.0.1"}, "dependencies": {"@skywind-group/sw-currency-exchange": "2.3.17", "@skywind-group/sw-deferred-payment": "1.17.0", "@skywind-group/sw-game-core": "0.9.22", "@skywind-group/sw-game-instant-jp": "0.0.24", "@skywind-group/sw-jpn-core": "1.20.8", "@skywind-group/sw-messaging": "0.2.5", "@skywind-group/sw-mrkt-ext": "1.0.2", "@skywind-group/sw-round-details-report": "1.1.2", "@skywind-group/sw-rtp-ext": "1.0.8", "@skywind-group/sw-utils": "2.5.0", "@skywind-group/sw-wallet": "1.0.3", "@skywind-group/sw-wallet-adapter-core": "2.1.2", "@skywind-group/sw_ex_feature_validation": "1.0.2", "@skywind-group/sw_ex_instant_jp": "0.1.11", "append-query": "2.1.1", "crc": "4.3.2", "superagent-proxy": "3.0.0", "emitter-listener": "1.1.2", "express-prom-bundle": "6.6.0", "prom-client": "14.2.0", "avsc": "5.4.7", "big.js": "3.2.0", "body-parser": "1.20.3", "cls-hooked": "^4.2.2", "commander": "2.19.0", "express-useragent": "1.0.15", "fastify": "2.15.3", "fastify-compress": "2.0.1", "fastify-cookie": "5.7.0", "fastify-static": "4.7.0", "hashids": "2.3.0", "js-big-integer": "1.0.2", "kafka-node": "5.0.0", "method-override": "3.0.0", "node-cache": "4.2.0", "pg": "8.13.3", "reflect-metadata": "0.1.13", "request-ip": "3.3.0", "sequelize": "6.37.6", "socket.io": "4.8.1", "socket.io-v2": "npm:socket.io@2.2.0", "socket.io-cookie-parser": "1.0.0", "source-map-support": "0.5.10"}}