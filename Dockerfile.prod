FROM node:14.21-buster

RUN sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list && \
    sed -i 's|http://security.debian.org/debian-security|http://archive.debian.org/debian-security|g' /etc/apt/sources.list && \
    sed -i '/stretch\/updates/d' /etc/apt/sources.list

ENV SONAR_SCANNER_VERSION 3.2.0.1227
ENV SONAR_SCANNER_PACKAGE sonar-scanner-cli-${SONAR_SCANNER_VERSION}-linux

# Install OpenJDK 8
RUN apt-get update && \
    apt install --yes apt-transport-https ca-certificates wget dirmngr gnupg software-properties-common && \
    apt --yes  install libgnutls30 && \
    wget --no-check-certificate -qO - https://packages.adoptium.net/artifactory/api/gpg/key/public | apt-key add - && \
    add-apt-repository --yes https://packages.adoptium.net/artifactory/deb/ && \
    apt update && \
    apt install --yes temurin-8-jdk ca-certificates-java

# Install dependencies
RUN apt-get -yqq --no-install-recommends install curl unzip

# Download sonar
RUN curl --insecure -OL https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/${SONAR_SCANNER_PACKAGE}.zip
RUN mkdir /root/.sonar/ /root/.sonar/native-sonar-scanner
RUN unzip ${SONAR_SCANNER_PACKAGE} -d /root/.sonar/native-sonar-scanner
RUN rm ${SONAR_SCANNER_PACKAGE}.zip

WORKDIR /app
COPY . /app/

CMD ["node", "/app/out/skywind/app"]
