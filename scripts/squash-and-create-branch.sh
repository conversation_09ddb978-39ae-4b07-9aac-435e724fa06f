#!/bin/bash

# Script to squash all commits in current branch to one and create a new branch
# Usage: ./squash-and-create-branch.sh [new-branch-name]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository!"
    exit 1
fi

# Get current branch name
CURRENT_BRANCH=$(git branch --show-current)
print_info "Current branch: $CURRENT_BRANCH"

# Check if we're on a feature branch (not develop or main/master)
if [[ "$CURRENT_BRANCH" == "develop" || "$CURRENT_BRANCH" == "main" || "$CURRENT_BRANCH" == "master" ]]; then
    print_error "Cannot squash commits on main branches (develop/main/master)"
    exit 1
fi

# Check if working directory is clean
if ! git diff-index --quiet HEAD --; then
    print_error "Working directory is not clean. Please commit or stash your changes."
    exit 1
fi

# Get the base branch (assuming it's develop)
BASE_BRANCH="develop"

# Check if base branch exists
if ! git show-ref --verify --quiet refs/heads/$BASE_BRANCH; then
    print_error "Base branch '$BASE_BRANCH' does not exist"
    exit 1
fi

# Get the number of commits ahead of base branch
COMMITS_AHEAD=$(git rev-list --count $BASE_BRANCH..HEAD)

if [ "$COMMITS_AHEAD" -eq 0 ]; then
    print_warning "No commits to squash. Current branch is up to date with $BASE_BRANCH"
    exit 0
fi

print_info "Found $COMMITS_AHEAD commits ahead of $BASE_BRANCH"

# Get new branch name from argument or generate one
if [ -n "$1" ]; then
    NEW_BRANCH="$1"
else
    # Generate new branch name by appending -squashed
    NEW_BRANCH="${CURRENT_BRANCH}-squashed"
fi

# Check if new branch already exists
if git show-ref --verify --quiet refs/heads/$NEW_BRANCH; then
    print_error "Branch '$NEW_BRANCH' already exists"
    exit 1
fi

print_info "Will create new branch: $NEW_BRANCH"

# Get the commit message from the most recent commit
COMMIT_MESSAGE=$(git log -1 --pretty=format:"%s")
print_info "Using commit message: $COMMIT_MESSAGE"

# Confirm with user
echo
print_warning "This will:"
echo "  1. Create a new branch '$NEW_BRANCH'"
echo "  2. Reset it to $BASE_BRANCH"
echo "  3. Squash all $COMMITS_AHEAD commits into one"
echo "  4. Use commit message: '$COMMIT_MESSAGE'"
echo
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Operation cancelled"
    exit 0
fi

# Store the current HEAD commit
CURRENT_HEAD=$(git rev-parse HEAD)

# Create and checkout new branch from base branch
print_info "Creating new branch '$NEW_BRANCH' from $BASE_BRANCH..."
git checkout -b $NEW_BRANCH $BASE_BRANCH

# Merge all commits from current branch without committing
print_info "Merging commits from $CURRENT_BRANCH..."
git merge --squash $CURRENT_HEAD

# Check if there are changes to commit
if git diff-index --quiet HEAD --; then
    print_warning "No changes to commit after merge"
    git checkout $CURRENT_BRANCH
    git branch -D $NEW_BRANCH
    exit 0
fi

# Commit the squashed changes
print_info "Committing squashed changes..."
git commit -m "$COMMIT_MESSAGE"

# Show the result
print_success "Successfully created branch '$NEW_BRANCH' with squashed commits"
print_info "Branch summary:"
echo "  - Original branch: $CURRENT_BRANCH ($COMMITS_AHEAD commits)"
echo "  - New branch: $NEW_BRANCH (1 commit)"
echo "  - Base: $BASE_BRANCH"

# Show the commit
echo
print_info "New commit:"
git log -1 --oneline

# Ask if user wants to switch back to original branch
echo
read -p "Switch back to original branch '$CURRENT_BRANCH'? (Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    git checkout $CURRENT_BRANCH
    print_info "Switched back to $CURRENT_BRANCH"
else
    print_info "Staying on $NEW_BRANCH"
fi

print_success "Operation completed successfully!"
echo
print_info "Next steps:"
echo "  - Review the squashed commit on '$NEW_BRANCH'"
echo "  - Push the new branch: git push origin $NEW_BRANCH"
echo "  - Create a pull request from '$NEW_BRANCH'"
