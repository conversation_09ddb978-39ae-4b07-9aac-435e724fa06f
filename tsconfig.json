{"compilerOptions": {"target": "es2019", "lib": ["es2019", "es2020.bigint", "es2020.string", "es2020.symbol.wellknown"], "sourceMap": false, "module": "commonjs", "moduleResolution": "node", "isolatedModules": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "noImplicitAny": false, "removeComments": true, "noLib": false, "preserveConstEnums": true, "inlineSources": false, "skipLibCheck": true, "outDir": "./lib"}, "include": ["src/**/*"], "exclude": ["node_modules"]}