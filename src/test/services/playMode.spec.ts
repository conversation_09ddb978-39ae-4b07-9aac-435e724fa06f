import { suite, test } from "mocha-typescript";
import { PlayMode } from "../../skywind/services/playMode";
import { expect } from "chai";
import { deepClone } from "../../skywind/utils/cloner";
import { init as initCurrencyExchangeRate } from "../../skywind/services/currencyexchange";
import { useFakeTimers } from "sinon";

@suite()
export class PlayModeSpec {

    private readonly clock = useFakeTimers();

    public static async before() {
        await initCurrencyExchangeRate();
    }

    public before() {
        this.clock.reset();
    }

    public after() {
        this.clock.restore();
    }

    @test()
    public isSupportJP() {
        expect(PlayMode.supportJP("real")).is.true;
        expect(PlayMode.supportJP(undefined)).is.true;
        expect(PlayMode.supportJP(null as any)).is.true;
        expect(PlayMode.supportJP("fun")).is.false;
        expect(PlayMode.supportJP("bns")).is.false;
        expect(PlayMode.supportJP("play_money")).is.false;
        expect(PlayMode.supportJP("fun_bonus")).is.false;
    }

    @test()
    public supportsHistory() {
        expect(PlayMode.supportsHistory("real")).is.true;
        expect(PlayMode.supportsHistory("bns")).is.true;
        expect(PlayMode.supportsHistory("fun")).is.true;
        expect(PlayMode.supportsHistory("play_money")).is.false;
        expect(PlayMode.supportsHistory("fun_bonus")).is.true;
    }

    @test()
    public determinesPlayModeByCurrency() {
        expect(PlayMode.getModeByCurrency("BNS")).equals("bns");
        expect(PlayMode.getModeByCurrency("USD")).equals("real");
        expect(PlayMode.getModeByCurrency("EUR")).equals("real");
        expect(PlayMode.getModeByCurrency("GBP")).equals("real");
        expect(PlayMode.getModeByCurrency("")).equals("real");
        expect(PlayMode.getModeByCurrency(null)).equals("real");
        expect(PlayMode.getModeByCurrency(undefined)).equals("real");
    }

    @test()
    public getCurrency() {
        expect(PlayMode.getCurrency({
            playmode: "real",
            currency: "USD"
        } as any)).to.be.equal("USD");

        expect(PlayMode.getCurrency({
            playmode: "fun",
            currency: "USD"
        } as any)).to.be.equal("USD");

        expect(PlayMode.getCurrency({
            playmode: "play_money",
            currency: "EUR"
        } as any)).to.be.equal("EUR");

        expect(PlayMode.getCurrency({
            playmode: "bns",
            currency: "USD"
        } as any)).to.be.equal("BNS");

        expect(PlayMode.getCurrency({
            playmode: "bns",
            currency: "EUR"
        } as any)).to.be.equal("BNS");
    }

    @test()
    public prepareContextForRealMode() {
        const origin = {
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD",
                }
            },
            round: {
                startedAt: 12345,
                finishedAt: 12345,
            },
            gameContext: {
                scene: "main"
            },
        };
        const ctx = deepClone(origin);
        PlayMode.clearContextAndCreateHistoryIfNeeded(ctx as any, {
            gameTokenData: {
                playmode: "real",
                currency: "USD",
            }
        } as any);

        expect(ctx).deep.equal(origin);
    }

    @test()
    public prepareContextForBNSModeWithSamePromoId() {
        const origin = {
            gameData: {
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                },
                bnsPromotion: {
                    promoId: "promo123"
                }
            },
            round: {
                startedAt: 12345,
                finishedAt: 12345,
            },
            gameContext: {
                scene: "main"
            },
        };
        const ctx = deepClone(origin);
        const result = PlayMode.clearContextAndCreateHistoryIfNeeded(ctx as any, {
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            },
            bnsPromotion: {
                promoId: "promo123"
            }
        } as any);

        expect(ctx).deep.equal(origin);
        expect(result.roundHistory).to.be.undefined;
        expect(result.gameEventHistory).to.be.undefined;
    }

    @test()
    public prepareContextForBNSModeWithDifferentPromoId() {
        const origin = {
            gameData: {
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                },
                bnsPromotion: {
                    promoId: "promo123"
                }
            },
            round: {
                startedAt: 12345,
                finishedAt: 12345,
            },
            gameContext: {
                scene: "main"
            },
            roundId: "round123",
            roundEnded: false,
            unfinished: true,
            session: {},
            id: {}
        };
        const ctx = deepClone(origin);
        const result = PlayMode.clearContextAndCreateHistoryIfNeeded(ctx as any, {
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            },
            bnsPromotion: {
                promoId: "promo456"
            }
        } as any);

        expect(ctx.round).to.be.undefined;
        expect(ctx.roundId).to.be.undefined;
        expect(ctx.roundEnded).to.be.true;
        expect(ctx.gameContext).to.be.undefined;
        expect(result.roundHistory).to.not.be.undefined;
        expect(result.gameEventHistory).to.not.be.undefined;
        expect(result.roundHistory.recoveryType).to.equal("finalize");
    }

    @test()
    public prepareContextForBNSMode_NoChanges() {
        const origin = {
            gameData: {
                bnsPromotion: {
                    promoId: 1
                },
                gameTokenData: {
                    playmode: "real",
                    currency: "USD",
                }
            },
            round: {
                startedAt: 12345,
                finishedAt: 12345,
            },
            roundId: 10,
            gameContext: {
                scene: "main"
            },
        };

        const ctx = deepClone(origin);
        PlayMode.clearContextAndCreateHistoryIfNeeded(ctx as any, {
            bnsPromotion: {
                promoId: 1
            },
            gameTokenData: {
                playmode: "real",
                currency: "USD",
            }
        } as any);

        expect(ctx).deep.equal(origin);

    }

    @test()
    public prepareContextForBNSMode_cleanupOld() {
        const origin = {
            gameData: {
                bnsPromotion: {
                    promoId: "1"
                },
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                }
            },
            round: {
                startedAt: 12345,
                finishedAt: 12345,
            },
            gameContext: {
                scene: "main"
            },
            roundId: 10,
        };

        const ctx = deepClone(origin);
        PlayMode.clearContextAndCreateHistoryIfNeeded(ctx as any, {
            bnsPromotion: {
                promoId: "2"
            },
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            }
        } as any);

        expect(ctx).deep.equal({
            gameData: {
                bnsPromotion: {
                    promoId: "1"
                },
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                },
            },
            round: undefined,
            roundId: undefined,
            roundEnded: true,
            gameContext: undefined
        });
    }

    @test()
    public async prepareContextForBNSMode_cleanupOldBroken() {
        const origin = {
            gameData: {
                bnsPromotion: {
                    promoId: "1"
                },
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                }
            },
            round: {
                startedAt: 12345,
                finishedAt: 12345,
            },
            gameContext: {
                scene: "main"
            },
            unfinished: true,
            roundId: 10,
            session: {},
            id: {}
        };

        const ctx = deepClone(origin);
        const { roundHistory, gameEventHistory } = PlayMode.clearContextAndCreateHistoryIfNeeded(ctx as any, {
            bnsPromotion: {
                promoId: "2"
            },
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            }
        } as any);
        expect(roundHistory).not.equal(undefined);
        expect(roundHistory.recoveryType).to.equal("finalize");
        expect(gameEventHistory).deep.include({
            roundEnded: true,
            type: "finalize"
        });
    }

    @test()
    public async exchange() {
        expect(PlayMode.exchange({
            gameTokenData: {
                playmode: "real",
                currency: "USD",
            }
        } as any, 100, "USD", "USD")).equal(100);

        expect(PlayMode.exchange({
            bnsPromotion: {
                promoId: "1",
                exchangeRate: 2
            },
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            }
        } as any, 100, "USD", "BNS")).equal(200);

        expect(PlayMode.exchange({
            bnsPromotion: {
                promoId: "1",
                exchangeRate: 2
            },
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            }
        } as any, 100, "USD", "EUR")).equal(114.64);

        expect(PlayMode.exchange({
            bnsPromotion: {
                promoId: "13",
                exchangeRate: 2
            },
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            }
        } as any, 100, "EUR", "BNS")).equal(174.44);

        expect(PlayMode.exchange({
            bnsPromotion: {
                promoId: "1",
                exchangeRate: 2
            },
            gameTokenData: {
                playmode: "bns",
                currency: "USD",
            }
        } as any, 100, "BNS", "EUR")).equal(57.32);

        expect(PlayMode.exchange({
            gameTokenData: {
                playmode: "play_money",
                currency: "XXX",
            }
        } as any, 678, "BNS", "EUR")).equal(678);
    }

    @test()
    public supportsSplitPayment() {
        expect(PlayMode.supportsSplitPayment("real")).is.true;
        expect(PlayMode.supportsSplitPayment("fun")).is.true;
        expect(PlayMode.supportsSplitPayment("play_money")).is.true;
        expect(PlayMode.supportsSplitPayment(undefined)).is.true;
        expect(PlayMode.supportsSplitPayment(null as any)).is.true;
        expect(PlayMode.supportsSplitPayment("bns")).is.false;
    }

    @test()
    public supportsRevert() {
        expect(PlayMode.supportsRevert("real")).is.true;
        expect(PlayMode.supportsRevert("fun")).is.true;
        expect(PlayMode.supportsRevert("play_money")).is.true;
        expect(PlayMode.supportsRevert(undefined)).is.true;
        expect(PlayMode.supportsRevert(null as any)).is.true;
        expect(PlayMode.supportsRevert("bns")).is.false;
    }
}
