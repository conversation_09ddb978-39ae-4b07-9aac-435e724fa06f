import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { useFakeTimers } from "sinon";
import { ContextFinalizationTimeCalculator } from "../../skywind/services/contextFinalizationTimeCalculator";
import config from "../../skywind/config";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import { addMinutes } from "../../skywind/utils/common";

@suite()
export class ContextFinalizationTimeCalculatorSpec {

    private readonly clock = useFakeTimers();

    public before() {
        this.clock.reset();
        this.clock.setSystemTime(0);
    }

    public after() {
        this.clock.restore();
    }

    @test()
    public noExpirationOnMissingExpirationInSettings() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                }
            }
        } as any)).is.undefined;
    }

    @test()
    public noExpirationForFunModeOnMissingExpirationInSettings() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "fun",
                    currency: "USD"
                }
            }
        } as any)).is.undefined;
    }

    @test()
    public lastPaymentDatePresentsShouldCalculateFromIt() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                }
            },
            settings: {
                roundExpireAt: 24
            },
            round: {
                lastSuccessfulPaymentDate: 100500
            }
        } as any)).to.be.equal(100500 + 24 * 60 * 1000);
    }

    @test()
    public lastPaymentDatePresentsShouldCalculateFromItFunMode() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "fun",
                    currency: "USD"
                }
            },
            settings: {
                roundExpireAt: 60
            },
            round: {
                lastSuccessfulPaymentDate: 15
            }
        } as any)).to.be.equal(3600015);
    }

    @test()
    public bnsModeExpirationShouldDependOnPromoExpiration() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                bnsPromotion: {
                    promoId: "1",
                    expireAt: 12345,
                },
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                }
            }
        } as any)).to.be.equal(12345);
    }

    @test()
    public bnsFinalizationFailedProlongExpireAt() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                bnsPromotion: {
                    promoId: "1",
                    expireAt: 12345,
                },
                gameTokenData: {
                    playmode: "bns",
                    currency: "USD",
                }
            },
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.be.equal(config.expireJob.postponeInterval * 60 * 1000);
    }

    @test()
    public funBonusModeExpirationShouldDependOnPromoExpiration() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                funBonusPromotion: {
                    promoId: "1",
                    expireAt: 54321,
                },
                gameTokenData: {
                    playmode: "fun_bonus",
                    currency: "USD",
                }
            }
        } as any)).to.be.equal(54321);
    }

    @test()
    public funBonusModeExpirationShouldReturnNullWhenNoPromotion() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "fun_bonus",
                    currency: "USD",
                }
            }
        } as any)).to.be.null;
    }

    @test()
    public funBonusFinalizationFailedProlongExpireAt() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                funBonusPromotion: {
                    promoId: "1",
                    expireAt: 54321,
                },
                gameTokenData: {
                    playmode: "fun_bonus",
                    currency: "USD",
                }
            },
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.be.equal(config.expireJob.postponeInterval * 60 * 1000);
    }

    @test()
    public finalizationFailedProlongExpireAt() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                }
            },
            settings: {
                roundExpireAt: 24
            },
            round: {
                lastSuccessfulPaymentDate: 100500
            },
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.be.equal(config.expireJob.postponeInterval * 60 * 1000);
    }

    @test()
    public noExpirationForApiCall() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                }
            },
            round: {
                lastSuccessfulPaymentDate: 100500
            },
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.be.equal(undefined);
    }

    @test()
    public nullExpirationForRoundEndedLongTermContext() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            persistencePolicy: GameContextPersistencePolicy.LONG_TERM
        } as any)).to.be.equal(null);
    }

    @test()
    public expirationWithFinalizationRetryPolicyInitialRetry() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15,
                finalizationRetryPolicy: {
                    factor: 2,
                    maxRetries: 10,
                    initialRetryTimeout: 15
                }
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 15));
    }

    @test()
    public expirationWithFinalizationRetryPolicyInitialRetryWithEnvDefaults() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 10));
    }

    @test()
    public expirationWithFinalizationRetryPolicyMultipleRetriesAlreadyDone() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15,
                finalizationRetryPolicy: {
                    factor: 2,
                    maxRetries: 10,
                    initialRetryTimeout: 15
                }
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 3
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 2 ** 3 * 15));
    }

    @test()
    public expirationWithFinalizationRetryPolicyMultipleRetriesAlreadyDoneWithEnvDefaults() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 3
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 2 ** 3 * 10));
    }

    @test()
    public expirationWithFinalizationRetryPolicyRetriesExceeded() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15,
                finalizationRetryPolicy: {
                    factor: 2,
                    maxRetries: 10,
                    initialRetryTimeout: 15
                }
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 12
                }
            }
        } as any)).to.equal(null);
    }

    @test()
    public expirationWithFinalizationRetryPolicyRetriesExceededWithEnvDefaults() {
        expect(ContextFinalizationTimeCalculator.getExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 16
                }
            }
        } as any)).to.equal(null);
    }

    @test()
    public nextExpirationWithFinalizationRetryPolicyInitialRetry() {
        expect(ContextFinalizationTimeCalculator.getNextExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15,
                finalizationRetryPolicy: {
                    factor: 2,
                    maxRetries: 10,
                    initialRetryTimeout: 10
                }
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 10));
    }

    @test()
    public nextExpirationWithFinalizationRetryPolicyInitialRetryWithEnvDefaults() {
        expect(ContextFinalizationTimeCalculator.getNextExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 10));
    }

    @test()
    public nextExpirationWithFinalizationRetryPolicyMultipleRetriesAlreadyDone() {
        expect(ContextFinalizationTimeCalculator.getNextExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15,
                finalizationRetryPolicy: {
                    factor: 2,
                    maxRetries: 10,
                    initialRetryTimeout: 10
                }
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 10
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 2 ** 10 * 10));
    }

    @test()
    public nextExpirationWithFinalizationRetryPolicyMultipleRetriesAlreadyDoneWithEnvDefaults() {
        expect(ContextFinalizationTimeCalculator.getNextExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 10
                }
            }
        } as any)).to.equal(addMinutes(Date.now(), 2 ** 10 * 10));
    }

    @test()
    public nextExpirationWithFinalizationRetryPolicyMaxRetriesExceeded() {
        expect(ContextFinalizationTimeCalculator.getNextExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15,
                finalizationRetryPolicy: {
                    factor: 2,
                    maxRetries: 10,
                    initialRetryTimeout: 10
                }
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 11
                }
            }
        } as any)).to.equal(null);
    }

    @test()
    public nextExpirationWithFinalizationRetryPolicyMaxRetriesExceededWithEnvDefaults() {
        expect(ContextFinalizationTimeCalculator.getNextExpiredAt({
            gameData: {
                gameTokenData: {
                    playmode: "real",
                    currency: "USD"
                },
                gameId: "sw_cl_965"
            },
            settings: {
                roundExpireAt: 15
            },
            roundEnded: true,
            pendingModification: {
                walletOperation: {
                    expireAtRevisionRequiredOnFail: true,
                    retry: 16
                }
            }
        } as any)).to.equal(null);
    }
}
