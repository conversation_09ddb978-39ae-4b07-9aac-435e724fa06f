import * as TokenUtils from "./tokens";
import { BrandSettings, GameSettings, GameTokenData, generateFunGameToken, Limits, Settings } from "./tokens";
import { ManagementAPISupport } from "./managementapihelper";
import { FunStartGameToken, GameMode, JurisdictionSettings } from "@skywind-group/sw-game-core";
import config from "../config";
import { lazy, logging, measures } from "@skywind-group/sw-utils";
import { Balance } from "./wallet";
import { getFunGameLimits } from "../utils/funGameLimits";
import { GameLaunchForbidden } from "../errors";
import { JackpotSettings } from "./jpn/jackpot";
import measure = measures.measure;

const log = logging.logger("sw-slot-engine:auth");

export interface PlayerInfo {
    code: string;
    status: string;
    firstName: string;
    lastName: string;
    nickname: string;
    email: string;
    currency: string;
    country: string;
    language: string;
    isTest: boolean;
    isVip?: boolean;
    isPublicChatBlock?: boolean;
    isPrivateChatBlock?: boolean;
    hasWarn?: boolean;
    nicknameChangeAttemptsLeft?: number;
}

export interface BrandInfo {
    name: string;
    title?: string;
}

export interface StartGameData {
    balance?: Balance;
    player?: PlayerInfo;
    limits: Limits;
    settings?: Settings;
    gameSettings?: GameSettings;
    brandSettings?: BrandSettings;
    jrsdSettings?: JurisdictionSettings;
    jurisdictionCode?: string;
    playedFromCountry?: string; // country that should be resolved from player's ip
    gameId?: string;
    region?: string;
    currencyReplacement?: string;
    jackpots?: JackpotSettings[];
    localizedGameName?: string;
    referrer?: string;
    renderType?: number; // ref HISTORY_RENDER_TYPE sw-management-api/packages/api/src/skywind/utils/common.ts
    brandInfo?: BrandInfo;
    operatorSiteId?: number; // foreign key to available site (operator site domain)
    ip?: string; // player's ip
    lobbySessionId?: string;
    operatorCountry?: string;
    operatorPlayerCountry?: string;
}

export interface BNSPromotion {
    promoId: string;
    expireAt: number;
    exchangeRate: number;
}

export interface FunBonusPromotion {
    promoId: string;
    expireAt?: number;
}

export enum LogoutType {
    ALL = "all",
    UNFINISHED = "unfinished"
}

export interface LogOutOptions {
    /**
     * Number of retries for logout request
     */
    maxRetryAttempts?: number;
    /**
     * Which type of games we need to notify about: all, unfinished
     */
    type?: LogoutType;

    /**
     * Custom session timeout
     */
    maxSessionTimeout?: number;
}

export interface GameData extends StartGameData {
    gameTokenData: GameTokenData;
    logoutOptions?: LogOutOptions;
}

export interface ClientSettings {
    brandSettings?: BrandSettings;
    gameSettings?: GameSettings;
}

export interface BNSGameData extends GameData {
    bnsPromotion?: BNSPromotion;
}

export interface FunBonusGameData extends GameData {
    funBonusPromotion?: FunBonusPromotion;
}

export interface StartGameResult {
    gameData: BNSGameData;
    localizedGameName?: string;
}

/**
 *  Authenticate player in Management API
 */
export interface AuthenticationService {
    startGame(gameId: string, startGameToken: string | FunStartGameToken, ip?: string, cookies?: string,
              language?: string, deviceId?: string, deviceData?: any, gameVersion?: string): Promise<StartGameResult>;
}

interface AuthenticateResponse extends StartGameData {
    gameToken: string;
    logoutOptions?: LogOutOptions;
}

/**
 *  Authentication service for real mode
 */
export class AuthenticationServiceImpl extends ManagementAPISupport implements AuthenticationService {
    private static START_GAME_URL = "/v2/play/startgame";

    constructor(url: string) {
        super(url);
    }

    @measure({ name: "AuthenticationService.startGame", isAsync: true })
    public async startGame(gameId: string,
                           startGameToken: string,
                           ip?: string,
                           cookies?: string,
                           language?: string,
                           deviceId?: string,
                           deviceData?: any,
                           gameVersion?: string): Promise<StartGameResult> {

        const response = await this.post<AuthenticateResponse>(
            AuthenticationServiceImpl.START_GAME_URL,
            {
                startGameToken: startGameToken,
                language,
                deviceId,
                deviceData,
                gameVersion
            }, ip, cookies);

        const tokenData = TokenUtils.parseGameToken(response.gameToken);
        return {
            gameData: {
                gameId: gameId,
                gameTokenData: tokenData,
                balance: response.balance,
                player: response.player,
                limits: response.limits,
                settings: response.settings,
                jrsdSettings: response.jrsdSettings,
                jurisdictionCode: response.jurisdictionCode,
                playedFromCountry: response.playedFromCountry,
                operatorPlayerCountry: response.operatorPlayerCountry,
                region: response.region,
                logoutOptions: response.logoutOptions,
                currencyReplacement: response.currencyReplacement,
                jackpots: response.jackpots,
                renderType: response.renderType,
                gameSettings: response.gameSettings,
                brandSettings: response.brandSettings,
                brandInfo: response.brandInfo,
                operatorSiteId: response.operatorSiteId
            },
            localizedGameName: response.localizedGameName
        };
    }
}

/**
 *  Authentication service for bonusCoins mode
 */
export class BNSAuthenticationServiceImpl extends AuthenticationServiceImpl {
    public async startGame(gameId: string,
                           startGameToken: string,
                           ip?: string,
                           cookies?: string,
                           language?: string,
                           deviceId?: string,
                           deviceData?: any): Promise<StartGameResult> {

        const result: StartGameResult = await super.startGame(gameId,
            startGameToken,
            ip,
            cookies,
            language,
            deviceId,
            deviceData);

        const bnsBalance = result.gameData.balance.bonusCoins;
        if (bnsBalance) {
            result.gameData.bnsPromotion = {
                promoId: bnsBalance.promoId,
                expireAt: Date.parse(bnsBalance.expireAt),
                exchangeRate: bnsBalance.exchangeRate
            };
        }
        return result;
    }
}

/**
 *  Authentication service for fun mode
 */
    // export class for test purposes
export class FunAuthenticationServiceImpl extends ManagementAPISupport implements AuthenticationService {
    private static START_GAME_URL = "/v2/play/fun/startgame";

    constructor(url: string) {
        super(url);
    }

    public async startGame(gameId: string,
                           startGameToken: FunStartGameToken,
                           ip?: string,
                           cookies?: string,
                           language?: string,
                           deviceId?: string): Promise<StartGameResult> {

        let result: StartGameResult;
        try {
            const response = await this.post<AuthenticateResponse>(
                FunAuthenticationServiceImpl.START_GAME_URL,
                {
                    startGameToken: startGameToken,
                    language,
                    deviceId
                }, ip, cookies);

            const gameTokenData = TokenUtils.parseGameToken(response.gameToken);

            result = {
                gameData: {
                    gameId: gameId,
                    gameTokenData,
                    balance: response.balance,
                    player: response.player,
                    limits: response.limits,
                    settings: response.settings,
                    jrsdSettings: response.jrsdSettings,
                    jurisdictionCode: response.jurisdictionCode,
                    playedFromCountry: response.playedFromCountry,
                    region: response.region,
                    jackpots: response.jackpots,
                    gameSettings: response.gameSettings,
                    brandSettings: response.brandSettings,
                    brandInfo: response.brandInfo,
                    operatorSiteId: response.operatorSiteId
                },
                localizedGameName: response.localizedGameName
            };
        } catch (err) {
            if (err.code === 802) {
                return Promise.reject(new GameLaunchForbidden());
            } else if (config.funGame.startGameTokenRequired) {
                return Promise.reject(err);
            }
            log.warn(err, "Error start game in fun mode");
            const limits: Limits = getFunGameLimits(startGameToken.currency);

            const gameTokenData: GameTokenData = {
                brandId: startGameToken.brandId,
                currency: startGameToken.currency,
                playerCode: startGameToken.playerCode,
                gameCode: startGameToken.gameCode,
                playmode: "fun"
            };

            gameTokenData.token = await generateFunGameToken(gameTokenData);

            result = {
                gameData: {
                    gameId: gameId,
                    gameTokenData: gameTokenData,
                    limits: limits
                }
            };
        }
        return result;
    }
}

const service = lazy(() => new AuthenticationServiceImpl(config.managementAPI.url));
const funService = lazy(() => new FunAuthenticationServiceImpl(config.managementAPI.url));
const bnsService = lazy(() => new BNSAuthenticationServiceImpl(config.managementAPI.url));

export function getService(mode: GameMode): AuthenticationService {
    switch (mode) {
        case "fun":
            return funService.get();
        case "bns":
            return bnsService.get();
        default:
            return service.get();
    }
}
